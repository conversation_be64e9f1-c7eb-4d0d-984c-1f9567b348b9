| 特性         | 手动创建的屏幕 (Manually Created)                  | 网络创建的屏幕 (Network Created)     |
|------------|---------------------------------------------|-------------------------------|
| 创建者        | 应用UI (用户)                                   | 网络客户端 (远程请求)                  |
| isManually | true                                        | false                         |
| UI持久性      | 用户移除前一直存在                                   | 通常与网络连接绑定，连接断开时和资源的销毁         |
| 网络占用       | 可被网络客户端占用，释放占用后回归空闲状态；客户端断开释放网络占用，屏幕保留为空闲状态 | 创建时即被特定网络客户端占用；屏幕（UI和资源）通常被销毁 |
| 复用性        | 主要设计为可被多个不同网络客户端先后复用                        | 通常不被其他客户端复用；生命周期与单个连接相关       |
| 主要目的       | 提供稳定、可复用的VD资源池，减少延迟和开销                      | 满足动态、临时的VD需求，或超出手动屏幕容量的需求     |



应用启动权限被拒绝：Permission Denial: starting Intent { flg=0x10200000 cmp=com.xxx.xxx/.MainActivity }
这是Android系统的正常安全机制: 普通应用无法在虚拟显示器上启动其他应用


