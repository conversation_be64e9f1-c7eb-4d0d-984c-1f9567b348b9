package com.example.virtualdisplaydemo.strategy;

import android.app.ActivityOptions;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import java.io.PrintWriter;

/**
 * Intent启动策略 - 使用Android Intent机制启动应用
 */
public class IntentLaunchStrategy implements AppLaunchStrategy {
    private static final String TAG = "IntentLaunchStrategy";
    
    private final Context mContext;
    private final Handler mServiceHandler;
    private final Handler mMainThreadHandler;
    
    public IntentLaunchStrategy(Context context, Handler serviceHandler, Handler mainThreadHandler) {
        mContext = context;
        mServiceHandler = serviceHandler;
        mMainThreadHandler = mainThreadHandler;
    }
    
    @Override
    public void launchApp(String packageName, String activityName, String displayId, 
                         String requestId, String connectionId, LaunchCallback callback, PrintWriter writer) {
        
        // 在主线程上启动Activity（Intent方式）
        mMainThreadHandler.post(() -> {
            try {
                Log.i(TAG, "ConnID: " + connectionId + " - 开始创建Intent: pkg=" + packageName + 
                      ", activity=" + activityName);
                
                // 创建Intent
                Intent launchIntent = createLaunchIntent(packageName, activityName);
                
                // 设置Intent标志
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
                
                // 设置显示器ID
                ActivityOptions options = createActivityOptions(displayId);
                
                Log.i(TAG, "ConnID: " + connectionId + " - 尝试通过Intent启动应用到显示器 " + displayId +
                        ", Component: " + launchIntent.getComponent() + ", options: " + options);
                
                if (options == null) {
                    throw new IllegalStateException("ActivityOptions创建失败");
                }
                
                mContext.startActivity(launchIntent, options.toBundle());
                
                Log.i(TAG, "ConnID: " + connectionId + " - Intent发送成功");
                callback.onLaunchSuccess(requestId);
                
            } catch (SecurityException e) {
                Log.e(TAG, "ConnID: " + connectionId + " - Intent启动安全异常: " + e.getMessage());
                callback.onLaunchFailed(requestId, "SecurityException: " + e.getMessage(), true);
                
            } catch (Exception e) {
                Log.e(TAG, "ConnID: " + connectionId + " - Intent启动异常: " + e.getMessage());
                callback.onLaunchFailed(requestId, "IntentException: " + e.getMessage(), false);
            }
        });
    }
    
    /**
     * 创建启动Intent
     */
    private Intent createLaunchIntent(String packageName, String activityName) {
        Intent launchIntent;
        String fullActivityName = activityName.startsWith(".") ? packageName + activityName : activityName;
        
        // 尝试使用PackageManager获取启动Intent
        PackageManager pm = mContext.getPackageManager();
        try {
            launchIntent = pm.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                Log.d(TAG, "使用PackageManager获取的启动Intent");
            } else {
                // 如果获取不到，手动创建
                launchIntent = new Intent();
                launchIntent.setComponent(new ComponentName(packageName, fullActivityName));
                Log.d(TAG, "手动创建Intent: " + fullActivityName);
            }
        } catch (Exception e) {
            // 如果PackageManager失败，手动创建
            launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(packageName, fullActivityName));
            Log.d(TAG, "PackageManager失败，手动创建Intent: " + fullActivityName);
        }
        
        return launchIntent;
    }
    
    /**
     * 创建ActivityOptions
     */
    private ActivityOptions createActivityOptions(String displayId) {
        ActivityOptions options = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            options = ActivityOptions.makeBasic();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && options != null) {
            options.setLaunchDisplayId(Integer.parseInt(displayId));
        }
        return options;
    }
}
