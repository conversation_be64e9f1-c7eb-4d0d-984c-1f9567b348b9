package com.example.virtualdisplaydemo.manager;

import android.app.ActivityOptions;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.NonNull;

import com.example.virtualdisplaydemo.strategy.AppLaunchStrategy;
import com.example.virtualdisplaydemo.strategy.IntentLaunchStrategy;
import com.example.virtualdisplaydemo.strategy.AdbLaunchStrategy;

import java.io.PrintWriter;

/**
 * 应用启动管理器 - 使用策略模式处理不同的应用启动方式
 */
public class AppLaunchManager {
    private static final String TAG = "AppLaunchManager";
    
    private final Context mContext;
    private final Handler mServiceHandler;
    private final Handler mMainThreadHandler;
    
    // 启动策略
    private final AppLaunchStrategy mIntentStrategy;
    private final AppLaunchStrategy mAdbStrategy;
    
    public AppLaunchManager(Context context, Handler serviceHandler, Handler mainThreadHandler) {
        mContext = context;
        mServiceHandler = serviceHandler;
        mMainThreadHandler = mainThreadHandler;
        
        // 初始化策略
        mIntentStrategy = new IntentLaunchStrategy(context, serviceHandler, mainThreadHandler);
        mAdbStrategy = new AdbLaunchStrategy(serviceHandler);
    }
    
    /**
     * 启动应用到指定显示器
     */
    public void launchAppOnDisplay(@NonNull String packageName, 
                                  @NonNull String activityName, 
                                  @NonNull String displayId, 
                                  @NonNull String requestId,
                                  @NonNull String connectionId,
                                  PrintWriter writer) {
        
        Log.i(TAG, "ConnID: " + connectionId + " - 尝试启动应用到显示器: " + 
              packageName + "/" + activityName + " -> Display " + displayId);
        
        // 首先尝试Intent方式
        mIntentStrategy.launchApp(packageName, activityName, displayId, requestId, connectionId, 
            new AppLaunchStrategy.LaunchCallback() {
                @Override
                public void onLaunchSuccess(String requestId) {
                    if (writer != null && !writer.checkError()) {
                        mServiceHandler.post(() -> {
                            writer.println("APP_LAUNCH_STATUS:" + requestId + ":OK:IntentSent");
                        });
                    }
                }
                
                @Override
                public void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb) {
                    if (canRetryWithAdb) {
                        Log.i(TAG, "Intent启动失败，尝试ADB方式: " + reason);
                        // 尝试ADB方式
                        mAdbStrategy.launchApp(packageName, activityName, displayId, requestId, connectionId,
                            new AppLaunchStrategy.LaunchCallback() {
                                @Override
                                public void onLaunchSuccess(String requestId) {
                                    // ADB策略会直接返回命令给客户端
                                }
                                
                                @Override
                                public void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb) {
                                    if (writer != null && !writer.checkError()) {
                                        mServiceHandler.post(() -> {
                                            writer.println("APP_LAUNCH_STATUS:" + requestId + ":ERROR:" + 
                                                          sanitizeErrorMessage(reason));
                                        });
                                    }
                                }
                            }, writer);
                    } else {
                        if (writer != null && !writer.checkError()) {
                            mServiceHandler.post(() -> {
                                writer.println("APP_LAUNCH_STATUS:" + requestId + ":ERROR:" + 
                                              sanitizeErrorMessage(reason));
                            });
                        }
                    }
                }
            }, writer);
    }
    
    /**
     * 清理错误消息中的特殊字符
     */
    private String sanitizeErrorMessage(String message) {
        if (message == null) return "null";
        return message.replaceAll(":", "_").replaceAll("\n", " ").replaceAll("\r", " ");
    }
    
    /**
     * 检查应用是否正在运行
     */
    public boolean isAppRunning(String packageName) {
        try {
            // 方法1: 检查应用进程是否存在
            boolean processExists = checkAppProcess(packageName);
            
            // 方法2: 检查应用是否在Activity栈中
            boolean inActivityStack = checkAppInActivityStack(packageName);
            
            // 只要有一种方法检测到应用存在就认为应用在运行
            boolean appRunning = processExists || inActivityStack;
            
            Log.d(TAG, "应用 " + packageName + " 运行状态: " + appRunning +
                    " (进程:" + processExists + ", 活动栈:" + inActivityStack + ")");
            return appRunning;
            
        } catch (Exception e) {
            Log.w(TAG, "检查应用运行状态时出错: " + e.getMessage());
            return true; // 出错时假设应用仍在运行，避免误断开
        }
    }
    
    /**
     * 检查应用进程是否存在
     */
    private boolean checkAppProcess(String packageName) {
        try {
            Process process = Runtime.getRuntime().exec("ps | grep " + packageName);
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream()));
            String line = reader.readLine();
            reader.close();
            process.waitFor();
            
            return line != null && line.contains(packageName);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查应用是否在Activity栈中
     */
    private boolean checkAppInActivityStack(String packageName) {
        try {
            Process process = Runtime.getRuntime().exec("dumpsys activity activities");
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream()));
            String line;
            boolean appFound = false;
            
            while ((line = reader.readLine()) != null) {
                // 更宽松的检测条件：只要在Activity相关的行中找到包名就认为应用在运行
                if (line.contains(packageName) &&
                        (line.contains("ActivityRecord") || line.contains("ResumedActivity") ||
                                line.contains("mResumedActivity") || line.contains("Running activities"))) {
                    appFound = true;
                    break;
                }
            }
            
            reader.close();
            process.waitFor();
            
            return appFound;
        } catch (Exception e) {
            return false;
        }
    }
}
