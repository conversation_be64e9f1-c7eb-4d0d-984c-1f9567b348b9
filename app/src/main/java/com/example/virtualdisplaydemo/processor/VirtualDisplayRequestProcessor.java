package com.example.virtualdisplaydemo.processor;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.example.virtualdisplaydemo.callback.ActivityCallback;
import com.example.virtualdisplaydemo.manager.ClientConnectionManager;
import com.example.virtualdisplaydemo.manager.NetworkServerManager;
import com.example.virtualdisplaydemo.manager.VirtualDisplayManager;
import com.example.virtualdisplaydemo.model.PendingVDRequest;
import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;

import java.io.PrintWriter;
import java.util.concurrent.TimeUnit;

/**
 * 虚拟显示请求处理器 - 专门处理虚拟显示的创建请求
 */
public class VirtualDisplayRequestProcessor {
    private static final String TAG = "VDRequestProcessor";
    
    private final VirtualDisplayManager mVirtualDisplayManager;
    private final ClientConnectionManager mConnectionManager;
    private final NetworkServerManager mNetworkServerManager;
    private final Handler mMainThreadHandler;
    
    public VirtualDisplayRequestProcessor(VirtualDisplayManager virtualDisplayManager,
                                        ClientConnectionManager connectionManager,
                                        NetworkServerManager networkServerManager) {
        mVirtualDisplayManager = virtualDisplayManager;
        mConnectionManager = connectionManager;
        mNetworkServerManager = networkServerManager;
        mMainThreadHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * 处理虚拟显示请求
     */
    public boolean handleVirtualDisplayRequest(PendingVDRequest request, String connectionId) 
            throws InterruptedException {
        
        final String userIdentifier = request.userIdentifier;
        final boolean forceCreateNew = request.forceCreateNew;
        final String serviceReqId = request.serviceRequestId;
        final PrintWriter writer = request.writer;
        
        ActivityCallback activityCallback = waitForActivityCallback(serviceReqId, writer);
        if (activityCallback == null) {
            return false;
        }
        
        // 尝试复用现有的手动VD
        if (!forceCreateNew) {
            VirtualDisplayContainer reusedVD = tryReuseManualVirtualDisplay(userIdentifier, 
                                                                           serviceReqId, writer, connectionId);
            if (reusedVD != null) {
                request.setResult(reusedVD.getInternalClientId(), String.valueOf(reusedVD.getDisplayId()));
                return true;
            }
        }
        
        // 创建新的VD
        return createNewVirtualDisplay(request, activityCallback, connectionId);
    }
    
    /**
     * 等待ActivityCallback可用
     */
    private ActivityCallback waitForActivityCallback(String serviceReqId, PrintWriter writer) {
        ActivityCallback activityCallback = mNetworkServerManager.getActivityCallback();
        
        if (activityCallback == null) {
            Log.w(TAG, "VD请求 (ReqID: " + serviceReqId + "): ActivityCallback为null，等待3秒后重试...");
            
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return null;
            }
            
            activityCallback = mNetworkServerManager.getActivityCallback();
            
            if (activityCallback == null) {
                Log.e(TAG, "VD请求 (ReqID: " + serviceReqId + "): ActivityCallback仍为null，MainActivity可能未正确绑定服务。");
                writer.println("ERROR:" + serviceReqId + ":DSMS Activity不可用 - MainActivity未绑定服务");
                return null;
            } else {
                Log.i(TAG, "VD请求 (ReqID: " + serviceReqId + "): ActivityCallback重试后可用。");
            }
        }
        
        return activityCallback;
    }
    
    /**
     * 尝试复用手动虚拟显示
     */
    private VirtualDisplayContainer tryReuseManualVirtualDisplay(String userIdentifier, 
                                                               String serviceReqId, 
                                                               PrintWriter writer,
                                                               String connectionId) {
        VirtualDisplayContainer reusedVD = mVirtualDisplayManager.findReusableManualVirtualDisplay(userIdentifier);
        if (reusedVD != null) {
            mConnectionManager.addNetworkConnection(reusedVD.getInternalClientId(), userIdentifier, connectionId);
            
            Log.i(TAG, "分配 (ReqID: " + serviceReqId + ")：成功复用手动VD (InternalID: " + 
                  reusedVD.getInternalClientId() + ") 给用户: " + userIdentifier);
            
            writer.println("VD_REUSED:" + reusedVD.getInternalClientId() + ":" + reusedVD.getDisplayId());
            
            // 更新UI状态
            updateManualScreenOccupationStatus(reusedVD.getInternalClientId(), 
                                             "网络:" + userIdentifier, true, reusedVD.getDisplayId());
            
            return reusedVD;
        }
        return null;
    }
    
    /**
     * 创建新的虚拟显示
     */
    private boolean createNewVirtualDisplay(PendingVDRequest request, 
                                          ActivityCallback activityCallback,
                                          String connectionId) throws InterruptedException {
        
        final String serviceReqId = request.serviceRequestId;
        final String userIdentifier = request.userIdentifier;
        final PrintWriter writer = request.writer;
        
        Log.i(TAG, "分配 (ReqID: " + serviceReqId + ")：创建新VD给用户: " + userIdentifier);
        writer.println("VD_PENDING:" + serviceReqId + ":请求已接收，处理VD创建...");
        
        final int assignedInternalId = mConnectionManager.generateNewInternalClientId();
        request.assignedInternalClientId = assignedInternalId;
        
        // 检查MediaProjection状态并请求创建
        if (!activityCallback.isMediaProjectionReady()) {
            Log.i(TAG, "新VD (ReqID: " + serviceReqId + "): MP未就绪。请求授权。");
            writer.println("VD_PENDING:" + serviceReqId + ":等待用户授予MP权限...");
            
            mMainThreadHandler.post(() -> activityCallback.requestMediaProjectionFromUser(serviceReqId));
        } else {
            Log.i(TAG, "新VD (ReqID: " + serviceReqId + "): MP已就绪。请求Activity创建UI。");
            
            mMainThreadHandler.post(() -> activityCallback.onRequestNewVirtualDisplayUI(
                serviceReqId, userIdentifier, assignedInternalId));
        }
        
        // 等待VD创建完成
        Log.d(TAG, "等待VD创建完成 (ReqID: " + serviceReqId + ")...");
        if (!request.completionLatch.await(120, TimeUnit.SECONDS)) {
            Log.e(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 等待VD创建或MP权限超时。");
            writer.println("ERROR:" + serviceReqId + ":等待DisplayID或权限超时。");
            return false;
        }
        
        // 检查结果
        if (request.wasMediaProjectionDenied()) {
            Log.w(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 失败: MP被用户拒绝。");
            writer.println("ERROR:" + serviceReqId + ":MediaProjection权限被拒绝。");
            return false;
        } else if (request.getResultingDisplayId() != null && 
                   !request.getResultingDisplayId().startsWith("ERROR_")) {
            
            mConnectionManager.addNetworkConnection(request.getAssignedInternalClientId(), 
                                                  userIdentifier, connectionId);
            
            Log.i(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 完成。DisplayID: " + 
                  request.getResultingDisplayId());
            writer.println("VD_CREATED:" + request.getAssignedInternalClientId() + ":" + 
                          request.getResultingDisplayId());
            
            // 更新UI状态
            try {
                int displayId = Integer.parseInt(request.getResultingDisplayId());
                updateManualScreenOccupationStatus(request.getAssignedInternalClientId(), 
                                                 "网络:" + userIdentifier, true, displayId);
            } catch (NumberFormatException e) {
                Log.e(TAG, "无法解析新创建的DisplayID: " + request.getResultingDisplayId(), e);
            }
            
            return true;
        } else {
            Log.e(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 失败: " + request.getResultingDisplayId());
            return false;
        }
    }
    
    /**
     * 更新手动屏幕占用状态
     */
    private void updateManualScreenOccupationStatus(int internalClientId, String occupyingUser, 
                                                   boolean isOccupied, int displayId) {
        mMainThreadHandler.post(() -> {
            ActivityCallback callback = mNetworkServerManager.getActivityCallback();
            if (callback != null) {
                callback.updateManualScreenOccupationStatus(internalClientId, occupyingUser, 
                                                           isOccupied, displayId);
            }
        });
    }
    
    /**
     * 清理资源
     */
    public void cleanup(PendingVDRequest pendingRequest, VirtualDisplayContainer reusedVD, 
                       String connectionId) {
        
        if (pendingRequest != null) {
            mConnectionManager.removePendingVDRequest(pendingRequest.serviceRequestId);
            
            if (pendingRequest.getAssignedInternalClientId() != -1 && 
                pendingRequest.getResultingDisplayId() != null && 
                !pendingRequest.getResultingDisplayId().startsWith("ERROR_")) {
                
                VirtualDisplayContainer vdToCheck = mVirtualDisplayManager.getVirtualDisplayContainer(
                    pendingRequest.getAssignedInternalClientId());
                
                if (vdToCheck != null && !vdToCheck.isManuallyCreated()) {
                    Log.i(TAG, "ClientSocketHandler (ConnID: " + connectionId + 
                          ") 结束，释放其创建的网络VD (InternalID: " + vdToCheck.getInternalClientId() + ")");
                    
                    mVirtualDisplayManager.releaseVirtualDisplay(vdToCheck.getInternalClientId());
                }
            }
        }
        
        if (reusedVD != null) {
            Log.i(TAG, "ClientSocketHandler (ConnID: " + connectionId + 
                  ") 为复用的手动VD (InternalID: " + reusedVD.getInternalClientId() + 
                  ") 执行完毕。释放网络占用。");
            
            reusedVD.releaseOccupation();
            mConnectionManager.removeNetworkConnection(reusedVD.getInternalClientId());
            
            updateManualScreenOccupationStatus(reusedVD.getInternalClientId(), null, false, 
                                             reusedVD.getDisplayId());
        }
    }
}
