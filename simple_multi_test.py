#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的多客户端测试脚本
"""

import subprocess
import time
import threading
import sys

def start_client(client_id, package_name, activity_name):
    """启动一个客户端"""
    try:
        print(f"[客户端 {client_id}] 启动中...")
        
        # 准备输入数据
        input_data = f"localhost\n\n{package_name}\n{activity_name}\n2\ny\n"
        
        # 启动客户端进程
        process = subprocess.Popen(
            ['python3', 'dct_client_refactored.py'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 发送输入数据
        print(f"[客户端 {client_id}] 发送输入数据: {repr(input_data)}")
        stdout, stderr = process.communicate(input=input_data, timeout=60)
        
        print(f"[客户端 {client_id}] 输出:")
        for line in stdout.split('\n'):
            if line.strip():
                print(f"  {line}")
        
        return process.returncode == 0
        
    except subprocess.TimeoutExpired:
        print(f"[客户端 {client_id}] 超时")
        process.kill()
        return False
    except Exception as e:
        print(f"[客户端 {client_id}] 异常: {e}")
        return False

def main():
    print("简单多客户端测试")
    print("-" * 50)
    
    test_apps = [
        ("com.wsy.crashcatcher", ".MainActivity"),
        ("xcrash.sample", ".MainActivity"),
        ("com.android.calculator2", ".Calculator"),
        ("com.android.calendar", ".AllInOneActivity"),]
    
    # 顺序启动客户端
    for i, (package, activity) in enumerate(test_apps):
        print(f"\n=== 测试客户端 {i+1}: {package} ===")
        success = start_client(i+1, package, activity)
        print(f"客户端 {i+1} {'成功' if success else '失败'}")
        
        if i < len(test_apps) - 1:
            print("等待5秒后启动下一个客户端...")
            time.sleep(5)
    
    print("\n测试完成")

if __name__ == "__main__":
    main()
