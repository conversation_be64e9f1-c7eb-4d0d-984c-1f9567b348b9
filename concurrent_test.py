#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
并发客户端测试脚本
"""

import subprocess
import time
import threading
import sys
import signal

class ConcurrentTester:
    def __init__(self):
        self.processes = []
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)

    def signal_handler(self, signum, frame):
        print(f"\n收到信号 {signum}，正在停止所有客户端...")
        self.stop_all()

    def stop_all(self):
        self.running = False
        for process in self.processes:
            if process.poll() is None:
                try:
                    process.terminate()
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    process.kill()
                except Exception as e:
                    print(f"停止进程时出错: {e}")

    def start_client_thread(self, client_id, package_name, activity_name):
        """在线程中启动客户端"""
        def run_client():
            try:
                print(f"[客户端 {client_id}] 启动中...")
                
                # 准备输入数据 - 使用强制新建策略
                input_data = f"localhost\n\n{package_name}\n{activity_name}\n2\ny\n"
                
                # 启动客户端进程
                process = subprocess.Popen(
                    ['python3', 'dct_client_refactored.py'],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True
                )
                
                self.processes.append(process)
                
                # 发送输入数据
                process.stdin.write(input_data)
                process.stdin.flush()
                process.stdin.close()
                
                # 监控输出
                while self.running and process.poll() is None:
                    line = process.stdout.readline()
                    if line:
                        print(f"[客户端 {client_id}] {line.strip()}")
                    else:
                        break
                
                print(f"[客户端 {client_id}] 进程结束，返回码: {process.returncode}")
                
            except Exception as e:
                print(f"[客户端 {client_id}] 异常: {e}")
        
        thread = threading.Thread(target=run_client, daemon=True)
        thread.start()
        return thread

    def test_concurrent_clients(self):
        """测试并发客户端"""
        print("=== 并发客户端测试 ===")
        
        test_apps = [
            ("com.android.settings", ".Settings"),
            ("com.android.calculator2", ".Calculator"),
        ]
        
        threads = []
        
        # 同时启动所有客户端
        for i, (package, activity) in enumerate(test_apps):
            if not self.running:
                break
                
            print(f"启动客户端 {i+1}: {package}")
            thread = self.start_client_thread(i+1, package, activity)
            threads.append(thread)
            time.sleep(2)  # 短暂延迟
        
        # 等待所有线程完成
        try:
            for thread in threads:
                thread.join(timeout=60)
        except KeyboardInterrupt:
            print("\n用户中断测试")
        finally:
            self.stop_all()

def main():
    print("并发客户端测试开始")
    print("按 Ctrl+C 停止测试")
    print("-" * 50)
    
    tester = ConcurrentTester()
    
    try:
        tester.test_concurrent_clients()
        print("\n等待30秒观察结果...")
        time.sleep(30)
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        tester.stop_all()
        print("测试结束")

if __name__ == "__main__":
    main()
